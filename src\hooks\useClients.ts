import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
	clientsApi,
	type CreateClientRequest,
	type UpdateClientRequest,
	type ClientsFilters,
	type Client,
	type ClientsResponse,
	type ClientDetailResponse,
} from "@/lib/api/clientsApi";
import {
	queryKeys,
	defaultMutationOptions,
	mediumLivedQueryOptions,
} from "@/lib/query";
import { useUIStore } from "@/stores/uiStore";
import type { Patient } from "@/components/ui-components/AllPatientListCard";

export const transformClientToPatient = (client: Client): Patient => {
	return {
		id: client.id.toString(),
		name: `${client.first_name} ${client.last_name}`,
		email: client.email,
		phone: client.phone_number,
		status: "Active",
		lastVisit: new Date(client.updated_at).toLocaleDateString("en-US", {
			day: "2-digit",
			month: "short",
			year: "numeric",
		}),
		syncStatus: "synced",
	};
};

export const transformClientDetailToPatientData = (clientDetail: any) => {
	const client = clientDetail.data;

	return {
		name: `${client.first_name} ${client.last_name}`,
		priority: "High",
		status: client.is_active ? "Active" : "Inactive",
		email: client.email,
		phone: client.phone_number,
		patientId: client.id.toString(),
		externalId: client.external_id || "",
		profilePictureUrl: client.profile_picture_url,
		lastVisit: client.last_visit
			? new Date(client.last_visit).toLocaleDateString("en-US", {
					day: "2-digit",
					month: "short",
					year: "numeric",
				})
			: "No visits",
		phoneVerified: client.phone_number_verified,
		categories: client.categories?.map((cat: any) => cat.name) || [],
		customIntake:
			client.attributes?.map((attr: any) => ({
				label: attr.label,
				value: attr.value?.toString() || "",
			})) || [],
		forms: [],
		messages: [],
	};
};

export const useClients = (
	filters: ClientsFilters = {},
	options?: {
		enabled?: boolean;
	}
) => {
	return useQuery<ClientsResponse>(
		queryKeys.clients.list(filters),
		() => clientsApi.getClients(filters),
		{
			...mediumLivedQueryOptions,
			enabled: options?.enabled !== false,
		}
	);
};

export const useClientDetail = (
	id: string | number,
	options?: {
		enabled?: boolean;
	}
) => {
	return useQuery<ClientDetailResponse>(
		queryKeys.clients.detail(id.toString()),
		() => clientsApi.getClientById(id),
		{
			...mediumLivedQueryOptions,
			enabled: options?.enabled !== false && !!id,
		}
	);
};

export const useCreateClient = (options?: {
	onSuccess?: (data: any) => void;
	onError?: (error: any) => void;
}) => {
	const queryClient = useQueryClient();
	const { addToast } = useUIStore();

	return useMutation<any, any, CreateClientRequest>({
		mutationFn: (data: CreateClientRequest) =>
			clientsApi.createClient(data),
		onSuccess: (data: any) => {
			addToast({
				type: "success",
				title: "Client Created",
				message: data.message || "Client has been created successfully",
			});
			queryClient.invalidateQueries({
				queryKey: queryKeys.clients.lists(),
			});

			options?.onSuccess?.(data);
		},
		onError: (error: any) => {
			options?.onError?.(error);
		},
		retry: 1,
		retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
	});
};

export const useUpdateClient = (options?: {
	onSuccess?: (data: any) => void;
	onError?: (error: any) => void;
}) => {
	const queryClient = useQueryClient();
	const { addToast } = useUIStore();

	return useMutation<
		any,
		any,
		{ id: string | number; data: UpdateClientRequest }
	>({
		mutationFn: ({
			id,
			data,
		}: {
			id: string | number;
			data: UpdateClientRequest;
		}) => clientsApi.updateClient(id, data),
		onSuccess: (data: any) => {
			addToast({
				type: "success",
				title: "Client Updated",
				message: "Client information has been updated successfully.",
			});

			queryClient.invalidateQueries({ queryKey: queryKeys.clients.all });

			if (data?.data?.id) {
				queryClient.invalidateQueries({
					queryKey: queryKeys.clients.detail(data.data.id.toString()),
				});
			}
			options?.onSuccess?.(data);
		},
		onError: (error: any) => {
			addToast({
				type: "error",
				title: "Update Failed",
				message:
					error?.message || "Failed to update client information.",
			});
			options?.onError?.(error);
		},
		retry: 1,
		retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
	});
};
